// src/pages/NewUnifiedSearch.jsx - New unified search page with centralized state management
import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  BookOpenIcon,
  RectangleStackIcon,
  ClockIcon,
  FunnelIcon,
  XMarkIcon,
  StarIcon,
  TagIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

import { useSearch } from '../contexts/SearchContext';
import { useSearchOperations } from '../hooks/useSearchOperations';
import SearchInput from '../components/SearchInput';
import DeckSearchResults from '../components/DeckSearchResults';
import CardSearchResults from '../components/CardSearchResults';
import SearchFilterChips from '../components/SearchFilterChips';
import SearchFilterPresets from '../components/SearchFilterPresets';
import Spinner from '../components/Spinner';
import Button from '../components/Button';
import PageHeader from '../components/PageHeader';
import PageTransition from '../components/PageTransition';

const SEARCH_TYPES = {
  DECKS: 'decks',
  CARDS: 'cards',
  ALL: 'all'
};

const NewUnifiedSearch = () => {
  const { state, actions } = useSearch();
  const {
    deckSearchQuery,
    cardSearchQuery,
    suggestionsQuery,
    executeSearch,
    executeQuickSearch,
    changePage,
    clearFilters,
    hasActiveFilters,
    isLoading,
    hasResults,
    totalResults
  } = useSearchOperations();

  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showSearchHistory, setShowSearchHistory] = useState(false);
  const [showFilterPresets, setShowFilterPresets] = useState(false);

  // Initialize from URL params
  useEffect(() => {
    const urlQuery = searchParams.get('q');
    const urlType = searchParams.get('type') || 'decks';
    
    if (urlQuery && urlQuery !== state.query) {
      actions.setQuery(urlQuery);
    }
    
    if (urlType !== state.searchType) {
      actions.setSearchType(urlType);
    }
  }, [searchParams, state.query, state.searchType, actions]);

  // Update URL when search state changes
  useEffect(() => {
    const params = new URLSearchParams();
    if (state.query) params.set('q', state.query);
    if (state.searchType) params.set('type', state.searchType);
    
    setSearchParams(params, { replace: true });
  }, [state.query, state.searchType, setSearchParams]);

  const handleSearch = (query) => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }
    
    executeSearch(query, state.searchType);
  };

  const handleSearchTypeChange = (type) => {
    actions.setSearchType(type);
    if (state.query) {
      executeSearch(state.query, type);
    }
  };

  const handleFilterChange = (filterName, value) => {
    const newFilters = {
      ...state.filters[state.searchType],
      [filterName]: value
    };
    actions.setFilters(newFilters, state.searchType);
    
    // Re-execute search if there's a query
    if (state.query) {
      executeSearch(state.query, state.searchType, newFilters);
    }
  };

  const handleClearFilters = () => {
    clearFilters(state.searchType);
    if (state.query) {
      executeSearch(state.query, state.searchType);
    }
  };

  const handleHistoryItemClick = (historyItem) => {
    actions.setQuery(historyItem.query);
    actions.setSearchType(historyItem.searchType);
    actions.setFilters(historyItem.filters, historyItem.searchType);
    executeSearch(historyItem.query, historyItem.searchType, historyItem.filters);
    setShowSearchHistory(false);
  };

  const handleRemoveFilter = (filterName, value) => {
    const newFilters = {
      ...state.filters[state.searchType],
      [filterName]: value
    };
    actions.setFilters(newFilters, state.searchType);

    // Re-execute search if there's a query
    if (state.query) {
      executeSearch(state.query, state.searchType, newFilters);
    }
  };

  const handleClearAllFilters = () => {
    clearFilters(state.searchType);
    if (state.query) {
      executeSearch(state.query, state.searchType);
    }
  };

  const renderSearchTabs = () => (
    <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
      {Object.entries(SEARCH_TYPES).map(([key, value]) => (
        <button
          key={value}
          onClick={() => handleSearchTypeChange(value)}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            state.searchType === value
              ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
          }`}
        >
          {value === 'decks' && <RectangleStackIcon className="h-4 w-4 mr-2" />}
          {value === 'cards' && <BookOpenIcon className="h-4 w-4 mr-2" />}
          {value === 'all' && <MagnifyingGlassIcon className="h-4 w-4 mr-2" />}
          {key.charAt(0) + key.slice(1).toLowerCase()}
          {state.results[value] && (
            <span className="ml-2 px-2 py-0.5 bg-gray-200 dark:bg-gray-600 text-xs rounded-full">
              {state.results[value].totalElements || 0}
            </span>
          )}
        </button>
      ))}
    </div>
  );

  const renderAdvancedFilters = () => {
    const currentFilters = state.filters[state.searchType];
    
    return (
      <AnimatePresence>
        {showAdvancedFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {state.searchType === 'decks' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Visibility
                    </label>
                    <select
                      value={currentFilters.isPublic === null ? 'all' : currentFilters.isPublic ? 'public' : 'private'}
                      onChange={(e) => {
                        const value = e.target.value === 'all' ? null : e.target.value === 'public';
                        handleFilterChange('isPublic', value);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      <option value="all">All Decks</option>
                      <option value="public">Public Only</option>
                      <option value="private">Private Only</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tags
                    </label>
                    <input
                      type="text"
                      placeholder="Enter tags (comma-separated)"
                      value={currentFilters.tagNames?.join(', ') || ''}
                      onChange={(e) => {
                        const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                        handleFilterChange('tagNames', tags);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="favoritesOnly"
                      checked={currentFilters.favoritesOnly || false}
                      onChange={(e) => handleFilterChange('favoritesOnly', e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="favoritesOnly" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                      Favorites Only
                    </label>
                  </div>
                </>
              )}
              
              {state.searchType === 'cards' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Difficulty Range
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        placeholder="Min"
                        min="1"
                        max="5"
                        value={currentFilters.minDifficulty || ''}
                        onChange={(e) => handleFilterChange('minDifficulty', e.target.value ? parseInt(e.target.value) : null)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      />
                      <input
                        type="number"
                        placeholder="Max"
                        min="1"
                        max="5"
                        value={currentFilters.maxDifficulty || ''}
                        onChange={(e) => handleFilterChange('maxDifficulty', e.target.value ? parseInt(e.target.value) : null)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeDueCards"
                      checked={currentFilters.includeDueCards || false}
                      onChange={(e) => handleFilterChange('includeDueCards', e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="includeDueCards" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                      Due for Review Only
                    </label>
                  </div>
                </>
              )}
            </div>
            
            <div className="mt-4 flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                disabled={!hasActiveFilters(state.searchType)}
              >
                Clear Filters
              </Button>
              
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {hasActiveFilters(state.searchType) && (
                  <span>{Object.values(state.filters[state.searchType]).filter(v => 
                    Array.isArray(v) ? v.length > 0 : v !== null && v !== undefined && v !== false
                  ).length} filters active</span>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  const renderSearchHistory = () => (
    <AnimatePresence>
      {showSearchHistory && state.searchHistory.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
        >
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Recent Searches</h3>
              <button
                onClick={() => actions.clearHistory()}
                className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                Clear All
              </button>
            </div>
          </div>
          
          <div className="py-2">
            {state.searchHistory.slice(0, 10).map((item, index) => (
              <button
                key={index}
                onClick={() => handleHistoryItemClick(item)}
                className="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-between"
              >
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-900 dark:text-gray-100">{item.query}</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    in {item.searchType}
                  </span>
                </div>
                <span className="text-xs text-gray-400">
                  {item.resultCount} results
                </span>
              </button>
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PageHeader
          title="Search"
          description="Find decks and cards across StudyCards"
          icon={<MagnifyingGlassIcon className="h-6 w-6" />}
        />
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Search Interface */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            {/* Search Input */}
            <div className="relative mb-6">
              <SearchInput
                placeholder={`Search ${state.searchType}...`}
                value={state.query}
                onChange={actions.setQuery}
                onSubmit={handleSearch}
                useAutocomplete={true}
                type={state.searchType}
                className="w-full"
                autoFocus
              />
              
              {/* Search History Button */}
              <button
                onClick={() => setShowSearchHistory(!showSearchHistory)}
                className="absolute right-12 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                title="Search History"
              >
                <ClockIcon className="h-5 w-5" />
              </button>
              
              {renderSearchHistory()}
            </div>
            
            {/* Search Type Tabs */}
            <div className="mb-6">
              {renderSearchTabs()}
            </div>

            {/* Active Filter Chips */}
            {hasActiveFilters(state.searchType) && (
              <div className="mb-4">
                <SearchFilterChips
                  filters={state.filters[state.searchType]}
                  searchType={state.searchType}
                  onRemoveFilter={handleRemoveFilter}
                  onClearAll={handleClearAllFilters}
                />
              </div>
            )}
            
            {/* Advanced Filters Toggle */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                  <span>Advanced Filters</span>
                  {showAdvancedFilters ? (
                    <ChevronUpIcon className="h-4 w-4" />
                  ) : (
                    <ChevronDownIcon className="h-4 w-4" />
                  )}
                  {hasActiveFilters(state.searchType) && (
                    <span className="px-2 py-0.5 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 text-xs rounded-full">
                      {Object.values(state.filters[state.searchType]).filter(v =>
                        Array.isArray(v) ? v.length > 0 : v !== null && v !== undefined && v !== false
                      ).length}
                    </span>
                  )}
                </button>

                <button
                  onClick={() => setShowFilterPresets(!showFilterPresets)}
                  className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                >
                  <StarIcon className="h-4 w-4" />
                  <span>Filter Presets</span>
                  {state.filterPresets.filter(p => p.searchType === state.searchType).length > 0 && (
                    <span className="px-2 py-0.5 bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400 text-xs rounded-full">
                      {state.filterPresets.filter(p => p.searchType === state.searchType).length}
                    </span>
                  )}
                </button>
              </div>

              {hasResults && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {totalResults.toLocaleString()} total results
                </div>
              )}
            </div>
            
            {/* Advanced Filters */}
            {renderAdvancedFilters()}

            {/* Filter Presets */}
            <AnimatePresence>
              {showFilterPresets && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4"
                >
                  <SearchFilterPresets
                    presets={state.filterPresets}
                    currentFilters={state.filters[state.searchType]}
                    searchType={state.searchType}
                    onSavePreset={actions.saveFilterPreset}
                    onLoadPreset={actions.loadFilterPreset}
                    onDeletePreset={actions.deleteFilterPreset}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <Spinner size="lg" />
              <span className="ml-3 text-gray-600 dark:text-gray-400">Searching...</span>
            </div>
          )}
          
          {/* Error State */}
          {state.error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8">
              <div className="flex items-center">
                <XMarkIcon className="h-5 w-5 text-red-400 mr-2" />
                <span className="text-red-800 dark:text-red-200">{state.error}</span>
              </div>
            </div>
          )}
          
          {/* Search Results */}
          {!isLoading && !state.error && (
            <>
              {/* Deck Results */}
              {(state.searchType === 'decks' || state.searchType === 'all') && state.results.decks && (
                <div className="mb-8">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <RectangleStackIcon className="h-5 w-5 mr-2" />
                    Decks ({state.results.decks.totalElements || 0})
                  </h2>
                  <DeckSearchResults
                    results={state.results.decks}
                    isLoading={deckSearchQuery.isLoading}
                    isError={deckSearchQuery.isError}
                    error={deckSearchQuery.error}
                    onPageChange={(page) => changePage(page, 'decks')}
                    hasValidCriteria={!!state.query || hasActiveFilters('decks')}
                    showDiscoveryFeatures={true}
                  />
                </div>
              )}
              
              {/* Card Results */}
              {(state.searchType === 'cards' || state.searchType === 'all') && state.results.cards && (
                <div className="mb-8">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <BookOpenIcon className="h-5 w-5 mr-2" />
                    Cards ({state.results.cards.totalElements || 0})
                  </h2>
                  <CardSearchResults
                    results={state.results.cards}
                    isLoading={cardSearchQuery.isLoading}
                    isError={cardSearchQuery.isError}
                    error={cardSearchQuery.error}
                    onPageChange={(page) => changePage(page, 'cards')}
                  />
                </div>
              )}
              
              {/* No Results */}
              {!hasResults && (state.query || hasActiveFilters(state.searchType)) && (
                <div className="text-center py-12">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-600 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    No results found
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Try adjusting your search terms or filters
                  </p>
                  <Button
                    variant="outline"
                    onClick={handleClearFilters}
                    disabled={!hasActiveFilters(state.searchType)}
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
              
              {/* Empty State */}
              {!state.query && !hasActiveFilters(state.searchType) && (
                <div className="text-center py-12">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-600 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Start your search
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Enter a search term or use filters to find {state.searchType}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </PageTransition>
  );
};

export default NewUnifiedSearch;
