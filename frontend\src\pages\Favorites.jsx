// src/pages/Favorites.jsx
import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import {
  StarIcon,
  HeartIcon,
  RectangleStackIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  XMarkIcon,
  TrashIcon,
  EyeIcon,
  PlayIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { motion, AnimatePresence } from 'framer-motion';

import { decksAPI } from '../api/decks';
import { useAuth } from '../hooks/useAuth';
import DeckCard from '../components/DeckCard';
import SearchInput from '../components/SearchInput';
import Button from '../components/Button';
import Spinner from '../components/Spinner';
import PageHeader from '../components/PageHeader';
import PageTransition from '../components/PageTransition';
import Pagination from '../components/Pagination';

const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list'
};

const SORT_OPTIONS = [
  { value: 'favoriteDate', label: 'Recently Favorited', direction: 'desc' },
  { value: 'createdAt', label: 'Date Created', direction: 'desc' },
  { value: 'updatedAt', label: 'Last Updated', direction: 'desc' },
  { value: 'title', label: 'Title (A-Z)', direction: 'asc' },
  { value: 'cardCount', label: 'Card Count', direction: 'desc' }
];

const Favorites = () => {
  const { currentUser } = useAuth();
  const queryClient = useQueryClient();
  
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDecks, setSelectedDecks] = useState(new Set());
  const [viewMode, setViewMode] = useState(VIEW_MODES.GRID);
  const [sortBy, setSortBy] = useState('favoriteDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(12);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Fetch favorites
  const {
    data: favoritesData,
    isLoading,
    error,
    refetch
  } = useQuery(
    ['userFavorites', page, pageSize, sortBy, sortDirection, searchQuery],
    async () => {
      const params = {
        page,
        size: pageSize,
        sortBy,
        direction: sortDirection
      };
      
      if (searchQuery) {
        // Use search API for filtered favorites
        return await decksAPI.searchFavorites(searchQuery, params);
      } else {
        // Use regular favorites API
        return await decksAPI.getFavorites(params);
      }
    },
    {
      keepPreviousData: true,
      staleTime: 30000, // 30 seconds
      onError: (error) => {
        console.error('Failed to load favorites:', error);
        toast.error('Failed to load your favorites');
      }
    }
  );

  // Bulk unfavorite mutation
  const bulkUnfavoriteMutation = useMutation(
    async (deckIds) => {
      const promises = deckIds.map(deckId => decksAPI.toggleFavorite(deckId));
      return await Promise.all(promises);
    },
    {
      onSuccess: () => {
        toast.success(`Removed ${selectedDecks.size} decks from favorites`);
        setSelectedDecks(new Set());
        setShowBulkActions(false);
        queryClient.invalidateQueries(['userFavorites']);
        queryClient.invalidateQueries(['enhancedDeckResponses']);
      },
      onError: (error) => {
        console.error('Bulk unfavorite failed:', error);
        toast.error('Failed to remove some decks from favorites');
      }
    }
  );

  // Event handlers
  const handleSearch = useCallback((query) => {
    setSearchQuery(query);
    setPage(0); // Reset to first page
  }, []);

  const handleSortChange = useCallback((newSortBy) => {
    const option = SORT_OPTIONS.find(opt => opt.value === newSortBy);
    setSortBy(newSortBy);
    setSortDirection(option?.direction || 'desc');
    setPage(0);
  }, []);

  const handleDeckSelect = useCallback((deckId, selected) => {
    const newSelected = new Set(selectedDecks);
    if (selected) {
      newSelected.add(deckId);
    } else {
      newSelected.delete(deckId);
    }
    setSelectedDecks(newSelected);
    setShowBulkActions(newSelected.size > 0);
  }, [selectedDecks]);

  const handleSelectAll = useCallback(() => {
    if (selectedDecks.size === favoritesData?.content?.length) {
      setSelectedDecks(new Set());
      setShowBulkActions(false);
    } else {
      const allIds = new Set(favoritesData?.content?.map(deck => deck.id) || []);
      setSelectedDecks(allIds);
      setShowBulkActions(true);
    }
  }, [selectedDecks.size, favoritesData?.content]);

  const handleBulkUnfavorite = useCallback(() => {
    if (selectedDecks.size === 0) return;
    
    const deckTitles = Array.from(selectedDecks)
      .map(id => favoritesData?.content?.find(deck => deck.id === id)?.title)
      .filter(Boolean)
      .slice(0, 3)
      .join(', ');
    
    const message = selectedDecks.size <= 3 
      ? `Remove "${deckTitles}" from favorites?`
      : `Remove ${selectedDecks.size} decks from favorites?`;
    
    if (window.confirm(message)) {
      bulkUnfavoriteMutation.mutate(Array.from(selectedDecks));
    }
  }, [selectedDecks, favoritesData?.content, bulkUnfavoriteMutation]);

  const handlePageChange = useCallback((newPage) => {
    setPage(newPage);
    setSelectedDecks(new Set()); // Clear selections when changing pages
    setShowBulkActions(false);
  }, []);

  // Render functions
  const renderToolbar = () => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Search */}
        <div className="flex-1 max-w-md">
          <SearchInput
            placeholder="Search your favorites..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSubmit={handleSearch}
            className="w-full"
          />
        </div>
        
        {/* Controls */}
        <div className="flex items-center space-x-3">
          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => handleSortChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
          >
            {SORT_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          {/* View Mode */}
          <div className="flex border border-gray-300 dark:border-gray-600 rounded-md">
            <button
              onClick={() => setViewMode(VIEW_MODES.GRID)}
              className={`p-2 ${viewMode === VIEW_MODES.GRID 
                ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400' 
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'}`}
              title="Grid View"
            >
              <Squares2X2Icon className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode(VIEW_MODES.LIST)}
              className={`p-2 border-l border-gray-300 dark:border-gray-600 ${viewMode === VIEW_MODES.LIST 
                ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400' 
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'}`}
              title="List View"
            >
              <ListBulletIcon className="h-4 w-4" />
            </button>
          </div>
          
          {/* Select All */}
          {favoritesData?.content?.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="flex items-center space-x-2"
            >
              <CheckIcon className="h-4 w-4" />
              <span>
                {selectedDecks.size === favoritesData?.content?.length ? 'Deselect All' : 'Select All'}
              </span>
            </Button>
          )}
        </div>
      </div>
      
      {/* Results count */}
      {favoritesData && (
        <div className="mt-3 text-sm text-gray-500 dark:text-gray-400">
          {favoritesData.totalElements} favorite{favoritesData.totalElements !== 1 ? 's' : ''}
          {searchQuery && ` matching "${searchQuery}"`}
        </div>
      )}
    </div>
  );

  const renderBulkActions = () => (
    <AnimatePresence>
      {showBulkActions && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 z-50"
        >
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {selectedDecks.size} deck{selectedDecks.size !== 1 ? 's' : ''} selected
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkUnfavorite}
              disabled={bulkUnfavoriteMutation.isLoading}
              className="flex items-center space-x-2 text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/20"
            >
              {bulkUnfavoriteMutation.isLoading ? (
                <Spinner size="sm" />
              ) : (
                <TrashIcon className="h-4 w-4" />
              )}
              <span>Remove from Favorites</span>
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedDecks(new Set());
                setShowBulkActions(false);
              }}
            >
              <XMarkIcon className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const renderDeckGrid = () => (
    <div className={`grid gap-6 ${
      viewMode === VIEW_MODES.GRID 
        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
        : 'grid-cols-1'
    }`}>
      {favoritesData?.content?.map((deck) => (
        <div key={deck.id} className="relative">
          {/* Selection checkbox */}
          <div className="absolute top-2 left-2 z-10">
            <input
              type="checkbox"
              checked={selectedDecks.has(deck.id)}
              onChange={(e) => handleDeckSelect(deck.id, e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
          
          <DeckCard
            deck={deck}
            showDiscoveryFeatures={true}
            className={selectedDecks.has(deck.id) ? 'ring-2 ring-primary-500' : ''}
          />
        </div>
      ))}
    </div>
  );

  const renderEmptyState = () => (
    <div className="text-center py-12">
      <StarIcon className="h-16 w-16 mx-auto text-gray-400 dark:text-gray-600 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {searchQuery ? 'No matching favorites' : 'No favorites yet'}
      </h3>
      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
        {searchQuery 
          ? `No favorites match "${searchQuery}". Try adjusting your search terms.`
          : 'Start building your collection by favoriting decks you find useful. Your favorites will appear here for easy access.'
        }
      </p>
      {searchQuery ? (
        <Button
          variant="outline"
          onClick={() => handleSearch('')}
        >
          Clear Search
        </Button>
      ) : (
        <Button
          onClick={() => window.location.href = '/app/discover'}
        >
          Discover Decks
        </Button>
      )}
    </div>
  );

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PageHeader
          title="My Favorites"
          description="Your collection of favorite decks"
          icon={<StarIconSolid className="h-6 w-6 text-yellow-500" />}
        />
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {renderToolbar()}
          {renderBulkActions()}
          
          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <Spinner size="lg" />
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading your favorites...</span>
            </div>
          )}
          
          {/* Error State */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
              <XMarkIcon className="h-8 w-8 text-red-400 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
                Failed to load favorites
              </h3>
              <p className="text-red-600 dark:text-red-300 mb-4">
                {error.message || 'Something went wrong while loading your favorites.'}
              </p>
              <Button onClick={() => refetch()}>
                Try Again
              </Button>
            </div>
          )}
          
          {/* Content */}
          {!isLoading && !error && (
            <>
              {favoritesData?.content?.length > 0 ? (
                <>
                  {renderDeckGrid()}
                  
                  {/* Pagination */}
                  {favoritesData.totalPages > 1 && (
                    <div className="mt-8">
                      <Pagination
                        currentPage={page}
                        totalPages={favoritesData.totalPages}
                        onPageChange={handlePageChange}
                        totalItems={favoritesData.totalElements}
                        itemsPerPage={pageSize}
                        onPageSizeChange={setPageSize}
                      />
                    </div>
                  )}
                </>
              ) : (
                renderEmptyState()
              )}
            </>
          )}
        </div>
      </div>
    </PageTransition>
  );
};

export default Favorites;
